package consts

// 通用错误码
const (
	// Success 成功
	Success = 200
	// CodeParameterError 参数错误
	CodeParameterError = 400
	// CodeUnauthorized 未授权
	CodeUnauthorized = 401
	// CodeForbidden 禁止访问
	CodeForbidden = 403
	// CodeNotFound 资源不存在
	CodeNotFound = 404
	// CodeMethodNotAllowed 方法不允许
	CodeMethodNotAllowed = 405
	// CodeConflict 资源冲突
	CodeConflict = 409
	// CodeInternalError 系统内部错误
	CodeInternalError = 500
)

// 认证相关错误码 (1000-1099)
const (
	// CodeAuthInvalidToken 认证令牌无效
	CodeAuthInvalidToken = 1001
	// CodeAuthMissingToken 缺少认证令牌
	CodeAuthMissingToken = 1002
	// CodeAuthFormatError 认证令牌格式错误
	CodeAuthFormatError = 1003
	// CodeAuthExpired 认证令牌已过期
	CodeAuthExpired = 1004
)

// 用户相关错误码 (1100-1199)
const (
	// CodeUserPasswordError 用户名或密码错误
	CodeUserPasswordError = 1101
	// CodeUserDisabled 用户已被封禁
	CodeUserDisabled = 1102
	// CodeUsernameExists 用户名已存在
	CodeUsernameExists = 1103
	// CodeEmailExists 邮箱已被注册
	CodeEmailExists = 1104
)

// GetMessage 获取错误消息
func GetMessage(code int) string {
	var messages = map[int]string{
		Success: "成功",

		// 通用
		CodeParameterError:   "参数格式错误",
		CodeUnauthorized:     "认证失败，请重新登录",
		CodeForbidden:        "无权访问",
		CodeNotFound:         "请求资源不存在",
		CodeMethodNotAllowed: "请求方法不允许",
		CodeConflict:         "资源冲突",
		CodeInternalError:    "系统内部错误",

		// 认证
		CodeAuthInvalidToken: "认证令牌无效",
		CodeAuthMissingToken: "缺少认证令牌",
		CodeAuthFormatError:  "认证令牌格式错误",
		CodeAuthExpired:      "认证令牌已过期",

		// 用户
		CodeUserPasswordError: "用户名或密码错误",
		CodeUserDisabled:      "该账户已被禁用",
		CodeUsernameExists:    "用户名已存在",
		CodeEmailExists:       "该邮箱已被注册",
	}

	if msg, ok := messages[code]; ok {
		return msg
	}
	return "未知错误"
} 