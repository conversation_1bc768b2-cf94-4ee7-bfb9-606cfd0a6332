package middleware

import (
	"github.com/gin-gonic/gin"
	"log"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/jwt"
	"strings"

	"shikeyinxiang/internal/consts"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			response.Error(c, consts.CodeAuthMissingToken)
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			response.Error(c, consts.CodeAuthFormatError)
			c.Abort()
			return
		}

		// 提取token
		tokenString := parts[1]
		if tokenString == "" {
			response.Error(c, consts.CodeAuthMissingToken)
			c.Abort()
			return
		}

		// 验证token（包含黑名单检查）
		valid, claims, errorMsg := jwt.ValidateToken(tokenString)
		if !valid {
			// 记录详细的验证失败信息
			if claims != nil {
				log.Printf("Token validation failed for user: ID=%d, Username=%s, Role=%s, Error=%s",
					claims.UserID, claims.Username, claims.Role, errorMsg)
			} else {
				log.Printf("Token validation failed: %s", errorMsg)
			}
			response.Error(c, consts.CodeAuthInvalidToken)
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("userId", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("token", tokenString)

		c.Next()
	}
}

// AdminOnlyMiddleware 管理员权限中间件
func AdminOnlyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			response.Error(c, consts.CodeUnauthorized)
			c.Abort()
			return
		}

		if role != "ADMIN" {
			response.Error(c, consts.CodeForbidden)
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetCurrentUserID 从上下文获取当前用户ID
func GetCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := c.Get("userId")
	if !exists {
		return 0, false
	}

	if id, ok := userID.(int64); ok {
		return id, true
	}

	return 0, false
}

// GetCurrentUsername 从上下文获取当前用户名
func GetCurrentUsername(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}

	if name, ok := username.(string); ok {
		return name, true
	}

	return "", false
}

// GetCurrentUserRole 从上下文获取当前用户角色
func GetCurrentUserRole(c *gin.Context) (string, bool) {
	role, exists := c.Get("role")
	if !exists {
		return "", false
	}

	if r, ok := role.(string); ok {
		return r, true
	}

	return "", false
}

// RequireAuth 检查是否已认证的辅助函数
func RequireAuth(c *gin.Context) bool {
	_, exists := c.Get("userId")
	return exists
}
