package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// INutritionAdviceService 营养建议服务接口，定义了所有与营养建议相关的业务能力
type INutritionAdviceService interface {
	// GetNutritionAdvice 根据用户营养统计获取个性化建议
	GetNutritionAdvice(ctx context.Context, userID int64, date string) ([]*v1.NutritionAdviceDisplayResponse, error)

	// GetAdviceByCondition 根据条件类型和百分比匹配建议
	GetAdviceByCondition(ctx context.Context, conditionType string, percentage int) (*v1.NutritionAdviceResponse, error)

	// GetDefaultAdvice 获取默认建议
	GetDefaultAdvice(ctx context.Context) (*v1.NutritionAdviceResponse, error)

	// CreateNutritionAdvice 创建营养建议（管理功能）
	CreateNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceManageReq) (*v1.NutritionAdviceResponse, error)

	// UpdateNutritionAdvice 更新营养建议
	UpdateNutritionAdvice(ctx context.Context, id int64, req *v1.NutritionAdviceUpdateReq) (*v1.NutritionAdviceResponse, error)

	// DeleteNutritionAdvice 删除营养建议
	DeleteNutritionAdvice(ctx context.Context, id int64) error

	// GetNutritionAdviceByID 根据ID获取营养建议
	GetNutritionAdviceByID(ctx context.Context, id int64) (*v1.NutritionAdviceResponse, error)

	// ListNutritionAdvice 获取营养建议列表（分页）
	ListNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceQueryReq) (*v1.NutritionAdviceListResponse, error)

	// GetAdvicesByConditionType 根据条件类型获取所有启用的营养建议
	GetAdvicesByConditionType(ctx context.Context, conditionType string) ([]*v1.NutritionAdviceResponse, error)

	// UpdateAdviceStatus 更新营养建议状态
	UpdateAdviceStatus(ctx context.Context, id int64, req *v1.NutritionAdviceStatusUpdateReq) error
}

// INutritionStatService 营养统计服务接口，定义了所有与营养统计相关的业务能力
type INutritionStatService interface {
	// GetDailyNutritionStat 获取用户每日营养统计
	GetDailyNutritionStat(ctx context.Context, userID int64, date string) (*v1.NutritionStatResponse, error)

	// GetNutritionTrend 获取用户营养趋势
	GetNutritionTrend(ctx context.Context, userID int64, req *v1.NutritionTrendReq) (*v1.NutritionTrendResponse, error)

	// GetNutritionDetails 获取用户营养摄入详情
	GetNutritionDetails(ctx context.Context, userID int64, date string) ([]*v1.NutritionDetailItemResponse, error)

	// CalculateNutritionComplianceRate 计算营养达标率
	CalculateNutritionComplianceRate(ctx context.Context, date string) (float64, error)

	// GetAllNutritionTrend 获取全体用户营养趋势
	GetAllNutritionTrend(ctx context.Context, period string) (map[string]interface{}, error)
}

// IHealthReportService 健康报告服务接口，定义了所有与健康报告相关的业务能力
type IHealthReportService interface {
	// GetHealthReport 获取用户健康报告
	GetHealthReport(ctx context.Context, userID int64, date string) (*v1.HealthReportResponse, error)

	// CalculateHealthScore 计算健康评分
	CalculateHealthScore(ctx context.Context, nutritionStat *v1.NutritionStatResponse, nutritionGoal *v1.UserNutritionGoalResponse) int

	// CalculateNutritionBalance 计算营养平衡数据
	CalculateNutritionBalance(ctx context.Context, nutritionStat *v1.NutritionStatResponse, nutritionGoal *v1.UserNutritionGoalResponse) *v1.NutritionBalanceResponse

	// CalculateWeeklyProgress 计算周进度对比数据
	CalculateWeeklyProgress(ctx context.Context, currentStat *v1.NutritionStatResponse, lastWeekStat *v1.NutritionStatResponse) *v1.WeeklyProgressResponse

	// GenerateHealthSummary 生成健康摘要建议
	GenerateHealthSummary(ctx context.Context, healthScore int, scoreChange int) string
}
