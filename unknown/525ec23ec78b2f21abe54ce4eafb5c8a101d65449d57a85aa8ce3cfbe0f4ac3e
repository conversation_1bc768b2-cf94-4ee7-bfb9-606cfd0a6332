package v1

import "time"

// AdminLoginReq 管理员登录请求
type AdminLoginReq struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"`  // 用户名
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"` // 密码
}

// UserLoginReq 普通用户登录请求
type UserLoginReq struct {
	Username string `json:"username,omitempty"`                                           // 用户名（兼容字段）
	Email    string `json:"email,omitempty"`                                              // 邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"` // 密码
}

// LoginRes 登录响应
type LoginRes struct {
	Token    string    `json:"token"`    // JWT令牌
	UserInfo *UserInfo `json:"userInfo"` // 用户信息
}

// RegisterReq 注册请求
type RegisterReq struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"`  // 用户名
	Email    string `json:"email" binding:"required" validate:"required,email,max=100"`    // 邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"` // 密码
}

// RegisterRes 注册响应
type RegisterRes struct {
	User *UserInfo `json:"user"` // 用户信息
}

// UserInfo 用户信息（不包含敏感信息）
type UserInfo struct {
	ID         int64     `json:"id"`
	Username   string    `json:"username"`
	Email      string    `json:"email"`
	Role       string    `json:"role"`
	Status     int8      `json:"status"`
	AvatarURL  string    `json:"avatarUrl,omitempty"`
	CreateTime time.Time `json:"createTime"`
}

// RefreshTokenRes 刷新令牌响应
type RefreshTokenRes struct {
	Token string `json:"token"`
}

// WechatLoginReq 微信登录请求
type WechatLoginReq struct {
	Code          string `json:"code" binding:"required" validate:"required"` // 微信授权码
	EncryptedData string `json:"encryptedData,omitempty"`                     // 用户敏感数据
	IV            string `json:"iv,omitempty"`                                // 偏移向量
}

// ChangePasswordReq 修改密码请求
type ChangePasswordReq struct {
	OldPassword string `json:"oldPassword" binding:"required" validate:"required,min=6,max=100"` // 旧密码
	NewPassword string `json:"newPassword" binding:"required" validate:"required,min=6,max=100"` // 新密码
}
