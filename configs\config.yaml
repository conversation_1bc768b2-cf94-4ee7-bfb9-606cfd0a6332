server:
  port: 8080
  mode: debug # debug, release, test

database:
  host: localhost
  port: 3306
  username: root
  password: "123456"
  dbname: dubbo_demo
  charset: utf8mb4
  parseTime: true
  loc: Local
  maxIdleConns: 10
  maxOpenConns: 100
  connMaxLifetime: 3600

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  poolSize: 10

jwt:
  secret: "your-secret-key-should-be-at-least-256-bits-long"
  expiration: 86400 # 24 hours in seconds

wechat:
  appid: "84ff2a7512b9e90a3eb836c"
  secret: "84ff2a7512b9e90a3eb836c"
  loginUrl: "https://api.weixin.qq.com/sns/jscode2session"

log:
  level: info # debug, info, warn, error
  format: json # json, text
  output: stdout # stdout, file
  filename: logs/app.log

cors:
  allowOrigins:
    - "*"
  allowMethods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowHeaders:
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"
    - "X-Requested-With"

file:
  uploadPath: "uploads"
  maxSize: 10485760 # 10MB
  allowedTypes:
    - "jpg"
    - "jpeg"
    - "png"
    - "gif"
    - "webp"

# Cloudflare R2 对象存储配置
r2:
  endpoint: "https://4f935fb48128c7489fee6eaa1ac41f6d.r2.cloudflarestorage.com"
  accessKeyId: "cdcda063335050fa6910dfb8dbcc3e70"
  secretAccessKey: "****************************************************************"
  bucketName: "user-avatar"
  region: "auto"
  allowedTypes:
    - "jpg"
    - "jpeg"
    - "png"
    - "gif"
    - "webp"
#  publicDomain: "https://your-custom-domain.com" # 可选：自定义域名
