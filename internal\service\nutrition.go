package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// INutritionAdviceService 营养建议服务接口，定义了所有与营养建议相关的业务能力
type INutritionAdviceService interface {
	// GetNutritionAdvice 根据用户营养统计获取个性化建议
	GetNutritionAdvice(ctx context.Context, userID int64, date string) ([]*v1.NutritionAdviceDisplayResponse, error)

	// GetAdviceByCondition 根据条件类型和百分比匹配建议
	GetAdviceByCondition(ctx context.Context, conditionType string, percentage int) (*v1.NutritionAdviceResponse, error)

	// GetDefaultAdvice 获取默认建议
	GetDefaultAdvice(ctx context.Context) (*v1.NutritionAdviceResponse, error)

	// CreateNutritionAdvice 创建营养建议（管理功能）
	CreateNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceManageReq) (*v1.NutritionAdviceResponse, error)

	// UpdateNutritionAdvice 更新营养建议
	UpdateNutritionAdvice(ctx context.Context, id int64, req *v1.NutritionAdviceUpdateReq) (*v1.NutritionAdviceResponse, error)

	// DeleteNutritionAdvice 删除营养建议
	DeleteNutritionAdvice(ctx context.Context, id int64) error

	// GetNutritionAdviceByID 根据ID获取营养建议
	GetNutritionAdviceByID(ctx context.Context, id int64) (*v1.NutritionAdviceResponse, error)

	// ListNutritionAdvice 获取营养建议列表（分页）
	ListNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceQueryReq) (*v1.NutritionAdviceListResponse, error)

	// GetAdvicesByConditionType 根据条件类型获取所有启用的营养建议
	GetAdvicesByConditionType(ctx context.Context, conditionType string) ([]*v1.NutritionAdviceResponse, error)

	// UpdateAdviceStatus 更新营养建议状态
	UpdateAdviceStatus(ctx context.Context, id int64, req *v1.NutritionAdviceStatusUpdateReq) error
}
