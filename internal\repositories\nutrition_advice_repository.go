package repositories

import (
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
)

// INutritionAdviceRepo 定义了营养建议仓库需要实现的所有方法
type INutritionAdviceRepo interface {
	// 基础CRUD操作
	Create(advice *entities.NutritionAdvice) error
	GetByID(id int64) (*entities.NutritionAdvice, error)
	Update(advice *entities.NutritionAdvice) error
	Delete(id int64) error

	// 查询方法
	FindByConditionTypeAndPercentage(conditionType string, percentage int) ([]*entities.NutritionAdvice, error)
	FindDefaultAdvice() (*entities.NutritionAdvice, error)
	FindByConditionType(conditionType string) ([]*entities.NutritionAdvice, error)
	FindAll(offset, limit int) ([]*entities.NutritionAdvice, int64, error)
	FindByStatus(status int8, offset, limit int) ([]*entities.NutritionAdvice, int64, error)

	// 管理方法
	UpdateStatus(id int64, status int8) error
	BatchUpdateStatus(ids []int64, status int8) error
	ExistsByTitle(title string) (bool, error)
	ExistsByTitleExcludeID(title string, excludeID int64) (bool, error)

	// 事务支持
	CreateWithTx(tx *gorm.DB, advice *entities.NutritionAdvice) error
	UpdateWithTx(tx *gorm.DB, advice *entities.NutritionAdvice) error
	DeleteWithTx(tx *gorm.DB, id int64) error
}

// nutritionAdviceRepository 营养建议仓储
type nutritionAdviceRepository struct {
	db *gorm.DB
}

// NewNutritionAdviceRepository 创建营养建议仓储实例
func NewNutritionAdviceRepository(db *gorm.DB) INutritionAdviceRepo {
	return &nutritionAdviceRepository{
		db: db,
	}
}

// 确保 nutritionAdviceRepository 实现了 INutritionAdviceRepo 接口
var _ INutritionAdviceRepo = &nutritionAdviceRepository{}

// Create 创建新营养建议
func (r *nutritionAdviceRepository) Create(advice *entities.NutritionAdvice) error {
	if err := r.db.Create(advice).Error; err != nil {
		return &DatabaseError{
			Operation: "create",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return nil
}

// GetByID 根据ID获取营养建议
func (r *nutritionAdviceRepository) GetByID(id int64) (*entities.NutritionAdvice, error) {
	var advice entities.NutritionAdvice
	if err := r.db.First(&advice, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &NotFoundError{
				Resource: "nutrition_advice",
				Field:    "id",
				Value:    id,
			}
		}
		return nil, &DatabaseError{
			Operation: "get",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return &advice, nil
}

// Update 更新营养建议
func (r *nutritionAdviceRepository) Update(advice *entities.NutritionAdvice) error {
	if err := r.db.Save(advice).Error; err != nil {
		return &DatabaseError{
			Operation: "update",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return nil
}

// Delete 删除营养建议
func (r *nutritionAdviceRepository) Delete(id int64) error {
	result := r.db.Delete(&entities.NutritionAdvice{}, id)
	if result.Error != nil {
		return &DatabaseError{
			Operation: "delete",
			Table:     "nutrition_advice",
			Err:       result.Error,
		}
	}
	if result.RowsAffected == 0 {
		return &NotFoundError{
			Resource: "nutrition_advice",
			Field:    "id",
			Value:    id,
		}
	}
	return nil
}

// FindByConditionTypeAndPercentage 根据条件类型和百分比查询适用的营养建议
func (r *nutritionAdviceRepository) FindByConditionTypeAndPercentage(conditionType string, percentage int) ([]*entities.NutritionAdvice, error) {
	var advices []*entities.NutritionAdvice
	err := r.db.Where("condition_type = ? AND min_percentage <= ? AND max_percentage >= ? AND status = 1", 
		conditionType, percentage, percentage).
		Order("priority DESC").
		Find(&advices).Error
	
	if err != nil {
		return nil, &DatabaseError{
			Operation: "find",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return advices, nil
}

// FindDefaultAdvice 获取默认建议
func (r *nutritionAdviceRepository) FindDefaultAdvice() (*entities.NutritionAdvice, error) {
	var advice entities.NutritionAdvice
	err := r.db.Where("is_default = 1 AND status = 1").
		Order("priority DESC").
		First(&advice).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &NotFoundError{
				Resource: "nutrition_advice",
				Field:    "is_default",
				Value:    true,
			}
		}
		return nil, &DatabaseError{
			Operation: "find",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return &advice, nil
}

// FindByConditionType 根据条件类型查询所有启用的营养建议
func (r *nutritionAdviceRepository) FindByConditionType(conditionType string) ([]*entities.NutritionAdvice, error) {
	var advices []*entities.NutritionAdvice
	err := r.db.Where("condition_type = ? AND status = 1", conditionType).
		Order("priority DESC").
		Find(&advices).Error
	
	if err != nil {
		return nil, &DatabaseError{
			Operation: "find",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return advices, nil
}

// FindAll 获取所有营养建议（分页）
func (r *nutritionAdviceRepository) FindAll(offset, limit int) ([]*entities.NutritionAdvice, int64, error) {
	var advices []*entities.NutritionAdvice
	var total int64
	
	// 获取总数
	if err := r.db.Model(&entities.NutritionAdvice{}).Count(&total).Error; err != nil {
		return nil, 0, &DatabaseError{
			Operation: "count",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	
	// 获取分页数据
	err := r.db.Order("priority DESC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&advices).Error
	
	if err != nil {
		return nil, 0, &DatabaseError{
			Operation: "find",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	
	return advices, total, nil
}

// FindByStatus 根据状态获取营养建议（分页）
func (r *nutritionAdviceRepository) FindByStatus(status int8, offset, limit int) ([]*entities.NutritionAdvice, int64, error) {
	var advices []*entities.NutritionAdvice
	var total int64
	
	// 获取总数
	if err := r.db.Model(&entities.NutritionAdvice{}).Where("status = ?", status).Count(&total).Error; err != nil {
		return nil, 0, &DatabaseError{
			Operation: "count",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	
	// 获取分页数据
	err := r.db.Where("status = ?", status).
		Order("priority DESC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&advices).Error
	
	if err != nil {
		return nil, 0, &DatabaseError{
			Operation: "find",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	
	return advices, total, nil
}

// UpdateStatus 更新营养建议状态
func (r *nutritionAdviceRepository) UpdateStatus(id int64, status int8) error {
	result := r.db.Model(&entities.NutritionAdvice{}).
		Where("id = ?", id).
		Update("status", status)
	
	if result.Error != nil {
		return &DatabaseError{
			Operation: "update",
			Table:     "nutrition_advice",
			Err:       result.Error,
		}
	}
	
	if result.RowsAffected == 0 {
		return &NotFoundError{
			Resource: "nutrition_advice",
			Field:    "id",
			Value:    id,
		}
	}
	
	return nil
}

// BatchUpdateStatus 批量更新营养建议状态
func (r *nutritionAdviceRepository) BatchUpdateStatus(ids []int64, status int8) error {
	if len(ids) == 0 {
		return nil
	}
	
	result := r.db.Model(&entities.NutritionAdvice{}).
		Where("id IN ?", ids).
		Update("status", status)
	
	if result.Error != nil {
		return &DatabaseError{
			Operation: "batch_update",
			Table:     "nutrition_advice",
			Err:       result.Error,
		}
	}
	
	return nil
}

// ExistsByTitle 检查标题是否已存在
func (r *nutritionAdviceRepository) ExistsByTitle(title string) (bool, error) {
	var count int64
	err := r.db.Model(&entities.NutritionAdvice{}).
		Where("title = ?", title).
		Count(&count).Error
	
	if err != nil {
		return false, &DatabaseError{
			Operation: "count",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	
	return count > 0, nil
}

// ExistsByTitleExcludeID 检查标题是否已存在（排除指定ID）
func (r *nutritionAdviceRepository) ExistsByTitleExcludeID(title string, excludeID int64) (bool, error) {
	var count int64
	err := r.db.Model(&entities.NutritionAdvice{}).
		Where("title = ? AND id != ?", title, excludeID).
		Count(&count).Error
	
	if err != nil {
		return false, &DatabaseError{
			Operation: "count",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	
	return count > 0, nil
}

// CreateWithTx 在事务中创建营养建议
func (r *nutritionAdviceRepository) CreateWithTx(tx *gorm.DB, advice *entities.NutritionAdvice) error {
	if err := tx.Create(advice).Error; err != nil {
		return &DatabaseError{
			Operation: "create_tx",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return nil
}

// UpdateWithTx 在事务中更新营养建议
func (r *nutritionAdviceRepository) UpdateWithTx(tx *gorm.DB, advice *entities.NutritionAdvice) error {
	if err := tx.Save(advice).Error; err != nil {
		return &DatabaseError{
			Operation: "update_tx",
			Table:     "nutrition_advice",
			Err:       err,
		}
	}
	return nil
}

// DeleteWithTx 在事务中删除营养建议
func (r *nutritionAdviceRepository) DeleteWithTx(tx *gorm.DB, id int64) error {
	result := tx.Delete(&entities.NutritionAdvice{}, id)
	if result.Error != nil {
		return &DatabaseError{
			Operation: "delete_tx",
			Table:     "nutrition_advice",
			Err:       result.Error,
		}
	}
	if result.RowsAffected == 0 {
		return &NotFoundError{
			Resource: "nutrition_advice",
			Field:    "id",
			Value:    id,
		}
	}
	return nil
}
