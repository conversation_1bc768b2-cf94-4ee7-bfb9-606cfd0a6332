package service

import (
	"context"
	"io"
)

// IFileService 文件服务接口，用于处理文件上传和访问
// 这是内部服务接口，被其他service调用，不直接暴露HTTP API
type IFileService interface {
	// GenerateUploadPresignedURL 生成文件上传的预签名URL
	//
	// 参数:
	//   - ctx: 上下文
	//   - id: 对象ID（可以是用户ID、食物ID等）
	//   - fileType: 文件类型（例如："avatar"表示用户头像，"foodimage"表示食物图片）
	//   - contentType: 文件的内容类型（例如："image/jpeg"）
	//   - expiration: URL的有效期（分钟）
	//
	// 返回值:
	//   - string: 预签名URL和文件名的组合字符串，格式为 "presignedUrl:::fileName"
	//   - error: 生成失败时返回错误
	GenerateUploadPresignedURL(ctx context.Context, id int64, fileType, contentType string, expiration int) (string, error)

	// GenerateDownloadPresignedURL 生成文件下载的预签名URL
	//
	// 参数:
	//   - ctx: 上下文
	//   - fileName: 文件名
	//   - expiration: URL的有效期（分钟）
	//
	// 返回值:
	//   - string: 下载用的预签名URL
	//   - error: 生成失败时返回错误
	GenerateDownloadPresignedURL(ctx context.Context, fileName string, expiration int) (string, error)

	// DeleteFile 删除指定的文件
	//
	// 参数:
	//   - ctx: 上下文
	//   - fileName: 要删除的文件名
	//
	// 返回值:
	//   - error: 删除失败时返回错误
	DeleteFile(ctx context.Context, fileName string) error

	// UploadFileToR2 直接上传文件到R2（代理上传，解决CORS问题）
	//
	// 参数:
	//   - ctx: 上下文
	//   - fileName: 文件名
	//   - file: 文件内容
	//   - contentType: 文件的内容类型
	//
	// 返回值:
	//   - error: 上传失败时返回错误
	UploadFileToR2(ctx context.Context, fileName string, file io.Reader, contentType string) error
}
