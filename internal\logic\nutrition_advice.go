package logic

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// nutritionAdviceLogic 营养建议业务逻辑实现
type nutritionAdviceLogic struct {
	nutritionAdviceRepo repositories.INutritionAdviceRepo
}

// NewNutritionAdviceLogic 创建营养建议业务逻辑实例
func NewNutritionAdviceLogic(
	nutritionAdviceRepo repositories.INutritionAdviceRepo,
) service.INutritionAdviceService {
	return &nutritionAdviceLogic{
		nutritionAdviceRepo: nutritionAdviceRepo,
	}
}

// 确保 nutritionAdviceLogic 实现了 INutritionAdviceService 接口
var _ service.INutritionAdviceService = &nutritionAdviceLogic{}

// GetNutritionAdvice 根据用户营养统计获取个性化建议
// 注意：此方法需要营养统计服务支持，当前返回默认建议
func (n *nutritionAdviceLogic) GetNutritionAdvice(ctx context.Context, userID int64, date string) ([]*v1.NutritionAdviceDisplayResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// TODO: 当营养统计服务实现后，这里应该获取用户的营养统计数据
	// 目前返回默认建议
	var adviceList []*v1.NutritionAdviceDisplayResponse

	// 尝试获取默认建议
	if defaultAdvice, err := n.GetDefaultAdvice(ctx); err == nil && defaultAdvice != nil {
		adviceList = append(adviceList, n.convertToDisplayResponse(defaultAdvice))
	} else {
		// 如果数据库中没有默认建议，使用硬编码的默认建议
		adviceList = append(adviceList, &v1.NutritionAdviceDisplayResponse{
			Type:        "info",
			Title:       "营养摄入基本合理",
			Description: "今日的营养摄入基本合理，保持均衡饮食有助于健康。",
		})
	}

	return adviceList, nil
}

// GetAdviceByCondition 根据条件类型和百分比匹配建议
func (n *nutritionAdviceLogic) GetAdviceByCondition(ctx context.Context, conditionType string, percentage int) (*v1.NutritionAdviceResponse, error) {
	if conditionType == "" {
		return nil, &ParameterError{Field: "conditionType", Message: "is required"}
	}

	advices, err := n.nutritionAdviceRepo.FindByConditionTypeAndPercentage(conditionType, percentage)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to find advice by condition: %w", err)
	}

	if len(advices) == 0 {
		return nil, nil // 没有匹配的建议
	}

	// 返回优先级最高的建议（已按优先级降序排列）
	return n.convertToResponse(advices[0]), nil
}

// GetDefaultAdvice 获取默认建议
func (n *nutritionAdviceLogic) GetDefaultAdvice(ctx context.Context) (*v1.NutritionAdviceResponse, error) {
	advice, err := n.nutritionAdviceRepo.FindDefaultAdvice()
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return nil, nil // 没有默认建议
		}
		return nil, fmt.Errorf("logic: failed to get default advice: %w", err)
	}

	return n.convertToResponse(advice), nil
}

// CreateNutritionAdvice 创建营养建议（管理功能）
func (n *nutritionAdviceLogic) CreateNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceManageReq) (*v1.NutritionAdviceResponse, error) {
	if req == nil {
		return nil, &ParameterError{Field: "req", Message: "is required"}
	}

	// 检查标题是否已存在
	exists, err := n.nutritionAdviceRepo.ExistsByTitle(req.Title)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check title existence: %w", err)
	}
	if exists {
		return nil, &BusinessError{Code: "TITLE_EXISTS", Message: "营养建议标题已存在"}
	}

	// 创建实体
	advice := entities.NewNutritionAdvice(req.Type, req.Title, req.Description, req.ConditionType, req.Priority)
	advice.SetPercentageRange(req.MinPercentage, req.MaxPercentage)
	advice.SetAsDefault(req.IsDefault)
	advice.SetStatus(req.Status)

	// 保存到数据库
	if err := n.nutritionAdviceRepo.Create(advice); err != nil {
		return nil, fmt.Errorf("logic: failed to create advice: %w", err)
	}

	return n.convertToResponse(advice), nil
}

// UpdateNutritionAdvice 更新营养建议
func (n *nutritionAdviceLogic) UpdateNutritionAdvice(ctx context.Context, id int64, req *v1.NutritionAdviceUpdateReq) (*v1.NutritionAdviceResponse, error) {
	if id <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}
	if req == nil {
		return nil, &ParameterError{Field: "req", Message: "is required"}
	}

	// 获取现有建议
	advice, err := n.nutritionAdviceRepo.GetByID(id)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return nil, &BusinessError{Code: "ADVICE_NOT_FOUND", Message: "营养建议不存在"}
		}
		return nil, fmt.Errorf("logic: failed to get advice: %w", err)
	}

	// 检查标题是否与其他建议冲突
	if req.Title != "" && req.Title != advice.Title {
		exists, err := n.nutritionAdviceRepo.ExistsByTitleExcludeID(req.Title, id)
		if err != nil {
			return nil, fmt.Errorf("logic: failed to check title existence: %w", err)
		}
		if exists {
			return nil, &BusinessError{Code: "TITLE_EXISTS", Message: "营养建议标题已存在"}
		}
	}

	// 更新字段
	if req.Type != "" {
		advice.Type = req.Type
	}
	if req.Title != "" {
		advice.UpdateContent(req.Title, advice.Description)
	}
	if req.Description != "" {
		advice.UpdateContent(advice.Title, req.Description)
	}
	if req.ConditionType != "" {
		advice.ConditionType = req.ConditionType
	}
	if req.MinPercentage != nil || req.MaxPercentage != nil {
		advice.SetPercentageRange(req.MinPercentage, req.MaxPercentage)
	}
	if req.IsDefault != nil {
		advice.SetAsDefault(*req.IsDefault)
	}
	if req.Priority != nil {
		advice.UpdatePriority(*req.Priority)
	}
	if req.Status != nil {
		advice.SetStatus(*req.Status)
	}

	// 保存更新
	if err := n.nutritionAdviceRepo.Update(advice); err != nil {
		return nil, fmt.Errorf("logic: failed to update advice: %w", err)
	}

	return n.convertToResponse(advice), nil
}

// DeleteNutritionAdvice 删除营养建议
func (n *nutritionAdviceLogic) DeleteNutritionAdvice(ctx context.Context, id int64) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}

	err := n.nutritionAdviceRepo.Delete(id)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return &BusinessError{Code: "ADVICE_NOT_FOUND", Message: "营养建议不存在"}
		}
		return fmt.Errorf("logic: failed to delete advice: %w", err)
	}

	return nil
}

// GetNutritionAdviceByID 根据ID获取营养建议
func (n *nutritionAdviceLogic) GetNutritionAdviceByID(ctx context.Context, id int64) (*v1.NutritionAdviceResponse, error) {
	if id <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	advice, err := n.nutritionAdviceRepo.GetByID(id)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return nil, &BusinessError{Code: "ADVICE_NOT_FOUND", Message: "营养建议不存在"}
		}
		return nil, fmt.Errorf("logic: failed to get advice: %w", err)
	}

	return n.convertToResponse(advice), nil
}

// ListNutritionAdvice 获取营养建议列表（分页）
func (n *nutritionAdviceLogic) ListNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceQueryReq) (*v1.NutritionAdviceListResponse, error) {
	if req == nil {
		return nil, &ParameterError{Field: "req", Message: "is required"}
	}

	// 设置默认分页参数
	current := req.Current
	if current <= 0 {
		current = 1
	}
	size := req.Size
	if size <= 0 {
		size = 10
	}

	offset := (current - 1) * size

	var advices []*entities.NutritionAdvice
	var total int64
	var err error

	// 根据查询条件获取数据
	if req.Status != nil {
		advices, total, err = n.nutritionAdviceRepo.FindByStatus(*req.Status, offset, size)
	} else {
		advices, total, err = n.nutritionAdviceRepo.FindAll(offset, size)
	}

	if err != nil {
		return nil, fmt.Errorf("logic: failed to list advices: %w", err)
	}

	// 转换为响应格式
	records := make([]*v1.NutritionAdviceResponse, len(advices))
	for i, advice := range advices {
		records[i] = n.convertToResponse(advice)
	}

	return &v1.NutritionAdviceListResponse{
		Total:   total,
		Records: records,
		Current: current,
		Size:    size,
	}, nil
}

// GetAdvicesByConditionType 根据条件类型获取所有启用的营养建议
func (n *nutritionAdviceLogic) GetAdvicesByConditionType(ctx context.Context, conditionType string) ([]*v1.NutritionAdviceResponse, error) {
	if conditionType == "" {
		return nil, &ParameterError{Field: "conditionType", Message: "is required"}
	}

	advices, err := n.nutritionAdviceRepo.FindByConditionType(conditionType)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get advices by condition type: %w", err)
	}

	responses := make([]*v1.NutritionAdviceResponse, len(advices))
	for i, advice := range advices {
		responses[i] = n.convertToResponse(advice)
	}

	return responses, nil
}

// UpdateAdviceStatus 更新营养建议状态
func (n *nutritionAdviceLogic) UpdateAdviceStatus(ctx context.Context, id int64, req *v1.NutritionAdviceStatusUpdateReq) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}
	if req == nil {
		return &ParameterError{Field: "req", Message: "is required"}
	}

	err := n.nutritionAdviceRepo.UpdateStatus(id, req.Status)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return &BusinessError{Code: "ADVICE_NOT_FOUND", Message: "营养建议不存在"}
		}
		return fmt.Errorf("logic: failed to update advice status: %w", err)
	}

	return nil
}

// convertToResponse 将实体转换为响应DTO
func (n *nutritionAdviceLogic) convertToResponse(advice *entities.NutritionAdvice) *v1.NutritionAdviceResponse {
	if advice == nil {
		return nil
	}

	return &v1.NutritionAdviceResponse{
		ID:            advice.ID,
		Type:          advice.Type,
		Title:         advice.Title,
		Description:   advice.Description,
		ConditionType: advice.ConditionType,
		MinPercentage: advice.MinPercentage,
		MaxPercentage: advice.MaxPercentage,
		IsDefault:     advice.IsDefault,
		Priority:      advice.Priority,
		Status:        advice.Status,
		CreatedAt:     advice.CreatedAt,
		UpdatedAt:     advice.UpdatedAt,
	}
}

// convertToDisplayResponse 将响应DTO转换为显示DTO
func (n *nutritionAdviceLogic) convertToDisplayResponse(advice *v1.NutritionAdviceResponse) *v1.NutritionAdviceDisplayResponse {
	if advice == nil {
		return nil
	}

	return &v1.NutritionAdviceDisplayResponse{
		Type:        advice.Type,
		Title:       advice.Title,
		Description: advice.Description,
	}
}
