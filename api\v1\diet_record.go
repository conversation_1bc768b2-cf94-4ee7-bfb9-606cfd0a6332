package v1

import "time"

// DietRecordQueryReq 饮食记录查询请求
type DietRecordQueryReq struct {
	Current   int     `form:"current" binding:"omitempty,min=1" validate:"omitempty,min=1"`                // 当前页，默认1
	Size      int     `form:"size" binding:"omitempty,min=1,max=100" validate:"omitempty,min=1,max=100"`   // 每页大小，默认10
	StartDate *string `form:"startDate" binding:"omitempty" validate:"omitempty"`                          // 开始日期 (YYYY-MM-DD)
	EndDate   *string `form:"endDate" binding:"omitempty" validate:"omitempty"`                            // 结束日期 (YYYY-MM-DD)
	MealType  *string `form:"mealType" binding:"omitempty,oneof=breakfast lunch dinner snacks" validate:"omitempty,oneof=breakfast lunch dinner snacks"` // 餐次类型筛选
	UserID    *int64  `form:"userId" binding:"omitempty,min=1" validate:"omitempty,min=1"`                 // 用户ID筛选（管理端使用）
}

// DietRecordCreateReq 创建饮食记录请求
type DietRecordCreateReq struct {
	Date     string                       `json:"date" binding:"required" validate:"required"`                                                                    // 记录日期 (YYYY-MM-DD)
	Time     string                       `json:"time" binding:"required" validate:"required"`                                                                    // 记录时间 (HH:MM:SS)
	MealType string                       `json:"mealType" binding:"required,oneof=breakfast lunch dinner snacks" validate:"required,oneof=breakfast lunch dinner snacks"` // 餐次类型
	Remark   *string                      `json:"remark" binding:"omitempty,max=200" validate:"omitempty,max=200"`                                             // 备注信息
	Foods    []DietRecordFoodCreateReq    `json:"foods" binding:"required,min=1,dive" validate:"required,min=1,dive"`                                         // 食物列表
}

// DietRecordUpdateReq 更新饮食记录请求
type DietRecordUpdateReq struct {
	ID       int64                        `json:"id" binding:"required,min=1" validate:"required,min=1"`                                                      // 饮食记录ID
	Date     string                       `json:"date" binding:"required" validate:"required"`                                                                // 记录日期 (YYYY-MM-DD)
	Time     string                       `json:"time" binding:"required" validate:"required"`                                                                // 记录时间 (HH:MM:SS)
	MealType string                       `json:"mealType" binding:"required,oneof=breakfast lunch dinner snacks" validate:"required,oneof=breakfast lunch dinner snacks"` // 餐次类型
	Remark   *string                      `json:"remark" binding:"omitempty,max=200" validate:"omitempty,max=200"`                                           // 备注信息
	Foods    []DietRecordFoodUpdateReq    `json:"foods" binding:"required,min=1,dive" validate:"required,min=1,dive"`                                       // 食物列表
}

// DietRecordFoodCreateReq 创建饮食记录食物明细请求
type DietRecordFoodCreateReq struct {
	FoodID   int64   `json:"foodId" binding:"required,min=1" validate:"required,min=1"`       // 食物ID
	FoodName string  `json:"foodName" binding:"required,max=50" validate:"required,max=50"`   // 食物名称
	Amount   float64 `json:"amount" binding:"required,min=0.01" validate:"required,min=0.01"` // 食物数量
	Unit     string  `json:"unit" binding:"required,max=20" validate:"required,max=20"`       // 计量单位
	Calories float64 `json:"calories" binding:"required,min=0" validate:"required,min=0"`     // 热量(千卡)
	Protein  float64 `json:"protein" binding:"required,min=0" validate:"required,min=0"`      // 蛋白质(g)
	Fat      float64 `json:"fat" binding:"required,min=0" validate:"required,min=0"`          // 脂肪(g)
	Carbs    float64 `json:"carbs" binding:"required,min=0" validate:"required,min=0"`        // 碳水化合物(g)
	Grams    float64 `json:"grams" binding:"required,min=0" validate:"required,min=0"`        // 食物克数
}

// DietRecordFoodUpdateReq 更新饮食记录食物明细请求
type DietRecordFoodUpdateReq struct {
	ID       *int64  `json:"id" binding:"omitempty,min=1" validate:"omitempty,min=1"`         // 食物明细ID（新增时为空）
	FoodID   int64   `json:"foodId" binding:"required,min=1" validate:"required,min=1"`       // 食物ID
	FoodName string  `json:"foodName" binding:"required,max=50" validate:"required,max=50"`   // 食物名称
	Amount   float64 `json:"amount" binding:"required,min=0.01" validate:"required,min=0.01"` // 食物数量
	Unit     string  `json:"unit" binding:"required,max=20" validate:"required,max=20"`       // 计量单位
	Calories float64 `json:"calories" binding:"required,min=0" validate:"required,min=0"`     // 热量(千卡)
	Protein  float64 `json:"protein" binding:"required,min=0" validate:"required,min=0"`      // 蛋白质(g)
	Fat      float64 `json:"fat" binding:"required,min=0" validate:"required,min=0"`          // 脂肪(g)
	Carbs    float64 `json:"carbs" binding:"required,min=0" validate:"required,min=0"`        // 碳水化合物(g)
	Grams    float64 `json:"grams" binding:"required,min=0" validate:"required,min=0"`        // 食物克数
}

// DietRecordResponse 饮食记录响应
type DietRecordResponse struct {
	ID              int64                       `json:"id"`              // 饮食记录ID
	UserID          int64                       `json:"userId"`          // 用户ID
	Date            time.Time                   `json:"date"`            // 记录日期
	Time            time.Time                   `json:"time"`            // 记录时间
	MealType        string                      `json:"mealType"`        // 餐次类型
	Remark          *string                     `json:"remark"`          // 备注信息
	TotalCalorie    float64                     `json:"totalCalorie"`    // 总热量(千卡)
	CreatedAt       time.Time                   `json:"createdAt"`       // 创建时间
	UpdatedAt       time.Time                   `json:"updatedAt"`       // 更新时间
	DietRecordFoods []DietRecordFoodResponse    `json:"dietRecordFoods"` // 食物明细列表
}

// DietRecordFoodResponse 饮食记录食物明细响应
type DietRecordFoodResponse struct {
	ID           int64     `json:"id"`           // 食物明细ID
	DietRecordID int64     `json:"dietRecordId"` // 饮食记录ID
	FoodID       int64     `json:"foodId"`       // 食物ID
	FoodName     string    `json:"foodName"`     // 食物名称
	Amount       float64   `json:"amount"`       // 食物数量
	Unit         string    `json:"unit"`         // 计量单位
	Calories     float64   `json:"calories"`     // 热量(千卡)
	Protein      float64   `json:"protein"`      // 蛋白质(g)
	Fat          float64   `json:"fat"`          // 脂肪(g)
	Carbs        float64   `json:"carbs"`        // 碳水化合物(g)
	Grams        float64   `json:"grams"`        // 食物克数
	CreatedAt    time.Time `json:"createdAt"`    // 创建时间
	Food         *FoodResponse `json:"food,omitempty"` // 关联的食物信息
}

// DietRecordListResponse 饮食记录列表响应（用于分页）
type DietRecordListResponse struct {
	Total   int64                 `json:"total"`   // 总记录数
	Records []*DietRecordResponse `json:"records"` // 饮食记录列表
	Current int                   `json:"current"` // 当前页码
	Size    int                   `json:"size"`    // 每页大小
}

// NutritionStatsQueryReq 营养统计查询请求
type NutritionStatsQueryReq struct {
	StartDate string  `form:"startDate" binding:"required" validate:"required"`                                                                               // 开始日期 (YYYY-MM-DD)
	EndDate   string  `form:"endDate" binding:"required" validate:"required"`                                                                                 // 结束日期 (YYYY-MM-DD)
	MealType  *string `form:"mealType" binding:"omitempty,oneof=breakfast lunch dinner snacks" validate:"omitempty,oneof=breakfast lunch dinner snacks"`   // 餐次类型筛选
	UserID    *int64  `form:"userId" binding:"omitempty,min=1" validate:"omitempty,min=1"`                                                                   // 用户ID筛选（管理端使用）
}

// NutritionStatsResponse 营养统计响应
type NutritionStatsResponse struct {
	StartDate    string                    `json:"startDate"`    // 统计开始日期
	EndDate      string                    `json:"endDate"`      // 统计结束日期
	TotalCalorie float64                   `json:"totalCalorie"` // 总热量(千卡)
	TotalProtein float64                   `json:"totalProtein"` // 总蛋白质(g)
	TotalFat     float64                   `json:"totalFat"`     // 总脂肪(g)
	TotalCarbs   float64                   `json:"totalCarbs"`   // 总碳水化合物(g)
	AvgCalorie   float64                   `json:"avgCalorie"`   // 平均每日热量
	AvgProtein   float64                   `json:"avgProtein"`   // 平均每日蛋白质
	AvgFat       float64                   `json:"avgFat"`       // 平均每日脂肪
	AvgCarbs     float64                   `json:"avgCarbs"`     // 平均每日碳水化合物
	DayCount     int                       `json:"dayCount"`     // 统计天数
	DailyStats   []DailyNutritionStats     `json:"dailyStats"`   // 每日营养统计
}

// DailyNutritionStats 每日营养统计
type DailyNutritionStats struct {
	Date         string                    `json:"date"`         // 日期 (YYYY-MM-DD)
	TotalCalorie float64                   `json:"totalCalorie"` // 当日总热量
	TotalProtein float64                   `json:"totalProtein"` // 当日总蛋白质
	TotalFat     float64                   `json:"totalFat"`     // 当日总脂肪
	TotalCarbs   float64                   `json:"totalCarbs"`   // 当日总碳水化合物
	MealStats    []MealNutritionStats      `json:"mealStats"`    // 各餐次营养统计
}

// MealNutritionStats 餐次营养统计
type MealNutritionStats struct {
	MealType     string  `json:"mealType"`     // 餐次类型
	TotalCalorie float64 `json:"totalCalorie"` // 餐次总热量
	TotalProtein float64 `json:"totalProtein"` // 餐次总蛋白质
	TotalFat     float64 `json:"totalFat"`     // 餐次总脂肪
	TotalCarbs   float64 `json:"totalCarbs"`   // 餐次总碳水化合物
}

// DietRecordBatchDeleteReq 批量删除饮食记录请求
type DietRecordBatchDeleteReq struct {
	IDs []int64 `json:"ids" binding:"required,min=1,dive,min=1" validate:"required,min=1,dive,min=1"` // 饮食记录ID列表
}
