package logic

import "fmt"

// Service层自定义错误类型
// 这些错误类型在Service层定义和使用，为Controller层提供业务相关的错误信息

// ParameterError 参数错误
type ParameterError struct {
	Field   string
	Message string
}

func (e *ParameterError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("parameter error: %s %s", e.Field, e.Message)
	}
	return fmt.Sprintf("parameter error: %s", e.Message)
}

// NotImplementedError 功能未实现错误
type NotImplementedError struct {
	Feature string
}

func (e *NotImplementedError) Error() string {
	return fmt.Sprintf("feature not implemented: %s", e.Feature)
}

// InvalidCredentialsError 无效凭据错误（用户名/邮箱或密码错误）
type InvalidCredentialsError struct{}

func (e *InvalidCredentialsError) Error() string {
	return "invalid credentials"
}

// AccountDisabledError 账户被禁用错误
type AccountDisabledError struct {
	UserID int64
}

func (e *AccountDisabledError) Error() string {
	return fmt.Sprintf("account disabled for user %d", e.UserID)
}

// InsufficientPermissionsError 权限不足错误
type InsufficientPermissionsError struct {
	RequiredRole string
	UserRole     string
}

func (e *InsufficientPermissionsError) Error() string {
	return fmt.Sprintf("insufficient permissions: required %s, got %s", e.RequiredRole, e.UserRole)
}

// UserAlreadyExistsError 用户已存在错误
type UserAlreadyExistsError struct {
	Field string
	Value string
}

func (e *UserAlreadyExistsError) Error() string {
	return fmt.Sprintf("user already exists with %s=%s", e.Field, e.Value)
}

// UserNotFoundError 用户未找到错误（业务层）
type UserNotFoundError struct {
	Identifier string
}

func (e *UserNotFoundError) Error() string {
	return fmt.Sprintf("user not found: %s", e.Identifier)
}

// WeakPasswordError 密码强度不足错误
type WeakPasswordError struct {
	Reason string
}

func (e *WeakPasswordError) Error() string {
	return fmt.Sprintf("password is too weak: %s", e.Reason)
}

// TokenGenerationError JWT令牌生成错误
type TokenGenerationError struct {
	Err error
}

func (e *TokenGenerationError) Error() string {
	return fmt.Sprintf("failed to generate token: %v", e.Err)
}

func (e *TokenGenerationError) Unwrap() error {
	return e.Err
}

// ExternalServiceError 外部服务错误（如微信API）
type ExternalServiceError struct {
	Service string
	Err     error
}

func (e *ExternalServiceError) Error() string {
	return fmt.Sprintf("external service %s error: %v", e.Service, e.Err)
}

func (e *ExternalServiceError) Unwrap() error {
	return e.Err
}

// FoodNotFoundError 食物未找到错误（业务层）
type FoodNotFoundError struct {
	FoodID int
}

func (e *FoodNotFoundError) Error() string {
	return fmt.Sprintf("food not found: id=%d", e.FoodID)
}

// FoodAlreadyExistsError 食物已存在错误
type FoodAlreadyExistsError struct {
	Field string
	Value string
}

func (e *FoodAlreadyExistsError) Error() string {
	return fmt.Sprintf("food already exists with %s=%s", e.Field, e.Value)
}

// FoodCategoryNotFoundError 食物分类未找到错误（业务层）
type FoodCategoryNotFoundError struct {
	CategoryID int
}

func (e *FoodCategoryNotFoundError) Error() string {
	return fmt.Sprintf("food category not found: id=%d", e.CategoryID)
}

// FoodCategoryAlreadyExistsError 食物分类已存在错误
type FoodCategoryAlreadyExistsError struct {
	Field string
	Value string
}

func (e *FoodCategoryAlreadyExistsError) Error() string {
	return fmt.Sprintf("food category already exists with %s=%s", e.Field, e.Value)
}

// InvalidNutritionDataError 无效营养数据错误
type InvalidNutritionDataError struct {
	Field   string
	Message string
}

func (e *InvalidNutritionDataError) Error() string {
	return fmt.Sprintf("invalid nutrition data: %s %s", e.Field, e.Message)
}

// CategoryInUseError 分类正在使用中错误
type CategoryInUseError struct {
	CategoryID int
	FoodCount  int64
}

func (e *CategoryInUseError) Error() string {
	return fmt.Sprintf("category %d is in use by %d foods, cannot delete", e.CategoryID, e.FoodCount)
}

// DietRecordNotFoundError 饮食记录未找到错误（业务层）
type DietRecordNotFoundError struct {
	DietRecordID int64
}

func (e *DietRecordNotFoundError) Error() string {
	return fmt.Sprintf("diet record not found: id=%d", e.DietRecordID)
}

// DietRecordAccessDeniedError 饮食记录访问被拒绝错误
type DietRecordAccessDeniedError struct {
	DietRecordID int64
	UserID       int64
}

func (e *DietRecordAccessDeniedError) Error() string {
	return fmt.Sprintf("access denied to diet record %d for user %d", e.DietRecordID, e.UserID)
}

// InvalidMealTypeError 无效餐次类型错误
type InvalidMealTypeError struct {
	MealType string
}

func (e *InvalidMealTypeError) Error() string {
	return fmt.Sprintf("invalid meal type: %s, must be one of: breakfast, lunch, dinner, snacks", e.MealType)
}

// InvalidDateRangeError 无效日期范围错误
type InvalidDateRangeError struct {
	StartDate string
	EndDate   string
}

func (e *InvalidDateRangeError) Error() string {
	return fmt.Sprintf("invalid date range: start=%s, end=%s", e.StartDate, e.EndDate)
}

// DuplicateDietRecordError 重复饮食记录错误
type DuplicateDietRecordError struct {
	UserID   int64
	Date     string
	MealType string
}

func (e *DuplicateDietRecordError) Error() string {
	return fmt.Sprintf("diet record already exists for user %d on %s for %s", e.UserID, e.Date, e.MealType)
}

// EmptyFoodListError 空食物列表错误
type EmptyFoodListError struct{}

func (e *EmptyFoodListError) Error() string {
	return "food list cannot be empty"
}

// InvalidFoodDataError 无效食物数据错误
type InvalidFoodDataError struct {
	Field   string
	Message string
}

func (e *InvalidFoodDataError) Error() string {
	return fmt.Sprintf("invalid food data: %s %s", e.Field, e.Message)
}
