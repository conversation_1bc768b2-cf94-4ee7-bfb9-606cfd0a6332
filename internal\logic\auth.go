package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/config"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/jwt"
	"shikeyinxiang/internal/pkg"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// WechatLoginResponse 微信登录响应
type WechatLoginResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionID    string `json:"unionid,omitempty"`
	ErrCode    int    `json:"errcode,omitempty"`
	ErrMsg     string `json:"errmsg,omitempty"`
}

// authLogic 认证业务逻辑实现
type authLogic struct {
	userRepo     repositories.IUserRepo
	wechatConfig *config.WechatConfig
	httpClient   *http.Client
}

// NewAuth 创建认证业务逻辑实例
func NewAuth(userRepo repositories.IUserRepo, wechatConfig *config.WechatConfig) service.IAuth {
	return &authLogic{
		userRepo:     userRepo,
		wechatConfig: wechatConfig,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// AdminLogin 管理员登录
func (a *authLogic) AdminLogin(ctx context.Context, req *v1.AdminLoginReq) (*v1.LoginRes, error) {
	// 参数验证
	if req.Username == "" {
		return nil, &ParameterError{Field: "username", Message: "is required"}
	}

	// 根据用户名查找用户
	user, err := a.userRepo.GetByUsername(req.Username)
	if err != nil {
		// 检查是否是用户未找到错误
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &InvalidCredentialsError{}
		}
		return nil, fmt.Errorf("service: failed to get user for admin login: %w", err)
	}

	// 验证密码
	if !pkg.VerifyPassword(user.Password, req.Password) {
		return nil, &InvalidCredentialsError{}
	}

	// 验证用户角色
	if user.Role != consts.RoleAdmin {
		return nil, &InsufficientPermissionsError{
			RequiredRole: consts.RoleAdmin,
			UserRole:     user.Role,
		}
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, &AccountDisabledError{UserID: user.ID}
	}

	// 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, &TokenGenerationError{Err: err}
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// UserLogin 普通用户登录
func (a *authLogic) UserLogin(ctx context.Context, req *v1.UserLoginReq) (*v1.LoginRes, error) {
	// 参数验证
	if req.Email == "" {
		return nil, &ParameterError{Field: "email", Message: "is required"}
	}

	// 根据邮箱查找用户
	user, err := a.userRepo.GetByEmail(req.Email)
	if err != nil {
		// 检查是否是用户未找到错误
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &InvalidCredentialsError{}
		}
		return nil, fmt.Errorf("service: failed to get user for login: %w", err)
	}

	// 验证密码
	if !pkg.VerifyPassword(user.Password, req.Password) {
		return nil, &InvalidCredentialsError{}
	}

	// 验证用户角色
	if user.Role != consts.RoleUser {
		return nil, &InsufficientPermissionsError{
			RequiredRole: consts.RoleUser,
			UserRole:     user.Role,
		}
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, &AccountDisabledError{UserID: user.ID}
	}

	// 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, &TokenGenerationError{Err: err}
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// WechatLogin 微信登录
func (a *authLogic) WechatLogin(ctx context.Context, req *v1.WechatLoginReq) (*v1.LoginRes, error) {
	// 1. 使用code向微信服务器获取openid
	wechatResp, err := a.getOpenIDByCode(req.Code)
	if err != nil {
		return nil, &ExternalServiceError{Service: "wechat", Err: err}
	}

	// 2. 根据openid查找用户
	user, err := a.userRepo.GetByOpenID(wechatResp.OpenID)
	if err != nil {
		// 检查是否是用户未找到错误
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			// 用户不存在，创建新用户
			user, err = a.createWechatUser(wechatResp.OpenID)
			if err != nil {
				return nil, fmt.Errorf("service: failed to create wechat user: %w", err)
			}
		} else {
			return nil, fmt.Errorf("service: failed to get user by openid: %w", err)
		}
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, &AccountDisabledError{UserID: user.ID}
	}

	// 3. 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, &TokenGenerationError{Err: err}
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// Register 用户注册
func (a *authLogic) Register(ctx context.Context, req *v1.RegisterReq) (*v1.RegisterRes, error) {
	// 检查用户名是否已存在
	exists, err := a.userRepo.ExistsByUsername(req.Username)
	if err != nil {
		return nil, fmt.Errorf("service: failed to check username exists: %w", err)
	}
	if exists {
		return nil, &UserAlreadyExistsError{Field: "username", Value: req.Username}
	}

	// 检查邮箱是否已存在
	exists, err = a.userRepo.ExistsByEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("service: failed to check email exists: %w", err)
	}
	if exists {
		return nil, &UserAlreadyExistsError{Field: "email", Value: req.Email}
	}

	// 加密密码
	hashedPassword, err := pkg.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("service: failed to hash password: %w", err)
	}

	// 创建用户实体
	user := entities.NewUser(req.Username, req.Email, hashedPassword, consts.RoleUser)

	// 保存用户
	if err := a.userRepo.Create(user); err != nil {
		// 检查是否是用户已存在错误
		var userExists *repositories.UserExistsError
		if errors.As(err, &userExists) {
			return nil, &UserAlreadyExistsError{Field: userExists.Field, Value: userExists.Value}
		}
		return nil, fmt.Errorf("service: failed to create user: %w", err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.RegisterRes{
		User: userInfo,
	}, nil
}

// Logout 用户登出
func (a *authLogic) Logout(ctx context.Context, token string) error {
	// 将token加入黑名单
	if err := jwt.BlacklistToken(token); err != nil {
		return fmt.Errorf("service: failed to blacklist token: %w", err)
	}
	return nil
}

// ChangePassword 修改密码
func (a *authLogic) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq, userID int64) (bool, error) {
	// 获取当前用户信息
	user, err := a.userRepo.GetByID(userID)
	if err != nil {
		// 检查是否是用户未找到错误
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return false, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return false, fmt.Errorf("service: failed to get user for password change: %w", err)
	}

	// 验证旧密码
	if !pkg.VerifyPassword(user.Password, req.OldPassword) {
		return false, &InvalidCredentialsError{}
	}

	// 加密新密码
	hashedPassword, err := pkg.HashPassword(req.NewPassword)
	if err != nil {
		return false, fmt.Errorf("service: failed to hash new password: %w", err)
	}

	// 更新密码
	if err := a.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		return false, fmt.Errorf("service: failed to update password: %w", err)
	}

	return true, nil
}

// GetCurrentUser 获取当前用户信息
func (a *authLogic) GetCurrentUser(ctx context.Context, userId int64) (*v1.UserInfo, error) {
	user, err := a.userRepo.GetByID(userId)
	if err != nil {
		// 检查是否是用户未找到错误
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userId)}
		}
		return nil, fmt.Errorf("service: failed to get current user: %w", err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return userInfo, nil
}

// createWechatUser 创建微信用户
func (a *authLogic) createWechatUser(openID string) (*entities.User, error) {
	// 生成随机用户名
	username := fmt.Sprintf("wx_%s", openID[:8])

	// 生成随机密码
	randomPassword := fmt.Sprintf("%d", time.Now().UnixNano())
	hashedPassword, err := pkg.HashPassword(randomPassword)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 设置占位邮箱
	email := fmt.Sprintf("%<EMAIL>", openID)

	// 创建用户实体
	user := &entities.User{
		Username: username,
		Email:    email,
		Password: hashedPassword,
		Role:     consts.RoleUser,
		Status:   consts.UserStatusActive,
		OpenID:   openID,
	}

	// 保存用户
	if err := a.userRepo.Create(user); err != nil {
		// 检查是否是用户已存在错误
		var userExists *repositories.UserExistsError
		if errors.As(err, &userExists) {
			return nil, &UserAlreadyExistsError{Field: userExists.Field, Value: userExists.Value}
		}
		return nil, err
	}

	return user, nil
}

// getOpenIDByCode 通过code获取用户openid
func (a *authLogic) getOpenIDByCode(code string) (*WechatLoginResponse, error) {
	// 构建请求URL
	requestURL := fmt.Sprintf("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		a.wechatConfig.LoginURL,
		a.wechatConfig.AppID,
		a.wechatConfig.Secret,
		url.QueryEscape(code))

	// 发送HTTP请求
	resp, err := a.httpClient.Get(requestURL)
	if err != nil {
		return nil, fmt.Errorf("failed to request wechat api: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read wechat response: %w", err)
	}

	// 解析JSON响应
	var wechatResp WechatLoginResponse
	if err := json.Unmarshal(body, &wechatResp); err != nil {
		return nil, fmt.Errorf("failed to parse wechat response: %w", err)
	}

	// 检查微信返回的错误
	if wechatResp.ErrCode != 0 {
		return nil, fmt.Errorf("wechat api error: code=%d, msg=%s", wechatResp.ErrCode, wechatResp.ErrMsg)
	}

	// 检查必要字段
	if wechatResp.OpenID == "" {
		return nil, fmt.Errorf("wechat response missing openid")
	}

	return &wechatResp, nil
}
