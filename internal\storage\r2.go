package storage

import (
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/google/uuid"

	"shikeyinxiang/internal/config"
)

type R2Storage struct {
	client *s3.S3
	config *config.R2Config
}

// NewR2Storage 创建R2存储客户端
func NewR2Storage(cfg *config.R2Config) (*R2Storage, error) {
	// 创建AWS会话配置，与Java版本保持一致
	awsConfig := &aws.Config{
		Credentials: credentials.NewStaticCredentials(
			cfg.AccessKeyID,
			cfg.SecretAccessKey,
			"", // token
		),
		Endpoint:         aws.String(cfg.Endpoint),
		Region:           aws.String(cfg.Region),
		S3ForcePathStyle: aws.Bool(true), // Cloudflare R2需要路径样式
		DisableSSL:       aws.Bool(false), // 确保使用HTTPS
	}

	// 创建会话
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %w", err)
	}

	// 创建S3客户端
	client := s3.New(sess)

	return &R2Storage{
		client: client,
		config: cfg,
	}, nil
}

// GenerateUploadPresignedURL 生成上传预签名URL
func (r *R2Storage) GenerateUploadPresignedURL(userID int64, fileType, contentType string, expiration time.Duration) (string, string, error) {
	// 检查文件类型是否允许
	ext := r.getExtensionFromContentType(contentType)
	if !r.isValidFileType(ext) {
		return "", "", fmt.Errorf("不支持的文件类型，允许的类型：%v", r.config.AllowedTypes)
	}

	// 生成唯一的文件名
	fileName := r.generateFileName(userID, fileType, ext)

	// 创建预签名上传请求，暂时不设置ContentType以避免签名不匹配
	putObjectInput := &s3.PutObjectInput{
		Bucket: aws.String(r.config.BucketName),
		Key:    aws.String(fileName),
		// 暂时注释掉ContentType，让前端自由设置
		// ContentType: aws.String(contentType),
	}

	req, _ := r.client.PutObjectRequest(putObjectInput)

	// 生成预签名URL，添加调试信息
	presignedURL, err := req.Presign(expiration)
	if err != nil {
		return "", "", fmt.Errorf("生成预签名URL失败：%w", err)
	}

	// 添加调试日志
	fmt.Printf("Generated presigned URL: %s\n", presignedURL)
	fmt.Printf("File name: %s\n", fileName)
	fmt.Printf("Content type: %s\n", contentType)
	fmt.Printf("Expiration: %v\n", expiration)

	return presignedURL, fileName, nil
}

// GenerateDownloadPresignedURL 生成下载预签名URL
func (r *R2Storage) GenerateDownloadPresignedURL(fileName string, expiration time.Duration) (string, error) {
	if fileName == "" {
		return "", fmt.Errorf("文件名不能为空")
	}

	// 检查文件是否存在
	_, err := r.client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(r.config.BucketName),
		Key:    aws.String(fileName),
	})
	if err != nil {
		return "", fmt.Errorf("文件不存在：%s", fileName)
	}

	// 创建预签名下载请求
	req, _ := r.client.GetObjectRequest(&s3.GetObjectInput{
		Bucket:               aws.String(r.config.BucketName),
		Key:                  aws.String(fileName),
		ResponseCacheControl: aws.String("public, max-age=86400"), // 添加缓存控制
	})

	// 生成预签名URL
	presignedURL, err := req.Presign(expiration)
	if err != nil {
		return "", fmt.Errorf("生成预签名URL失败：%w", err)
	}

	return presignedURL, nil
}

// generateFileName 生成文件名
func (r *R2Storage) generateFileName(userID int64, fileType, extension string) string {
	timestamp := time.Now().Format("20060102150405")
	uniqueID := uuid.New().String()[:8]
	return fmt.Sprintf("%s/%d/%s_%s%s", fileType, userID, timestamp, uniqueID, extension)
}

// getExtensionFromContentType 从Content-Type获取文件扩展名
func (r *R2Storage) getExtensionFromContentType(contentType string) string {
	switch contentType {
	case "image/jpeg":
		return ".jpg"
	case "image/png":
		return ".png"
	case "image/gif":
		return ".gif"
	case "image/webp":
		return ".webp"
	default:
		return ""
	}
}

// isValidFileType 检查文件类型是否有效
func (r *R2Storage) isValidFileType(extension string) bool {
	if extension == "" {
		return false
	}

	// 移除点号
	ext := strings.TrimPrefix(extension, ".")

	for _, allowedType := range r.config.AllowedTypes {
		if strings.TrimSpace(allowedType) == ext {
			return true
		}
	}

	return false
}

// DeleteFile 删除指定的文件
func (r *R2Storage) DeleteFile(fileName string) error {
	if fileName == "" {
		return fmt.Errorf("文件名不能为空")
	}

	// 检查文件是否存在
	_, err := r.client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(r.config.BucketName),
		Key:    aws.String(fileName),
	})
	if err != nil {
		// 如果文件不存在，直接返回，不抛出异常（与Java版本行为一致）
		return nil
	}

	// 删除文件
	_, err = r.client.DeleteObject(&s3.DeleteObjectInput{
		Bucket: aws.String(r.config.BucketName),
		Key:    aws.String(fileName),
	})
	if err != nil {
		return fmt.Errorf("删除文件失败：%w", err)
	}

	return nil
}

// UploadFile 直接上传文件到R2
func (r *R2Storage) UploadFile(fileName string, file io.Reader, contentType string) error {
	if fileName == "" {
		return fmt.Errorf("文件名不能为空")
	}
	if file == nil {
		return fmt.Errorf("文件内容不能为空")
	}

	// 将io.Reader转换为io.ReadSeeker
	var body io.ReadSeeker
	if rs, ok := file.(io.ReadSeeker); ok {
		body = rs
	} else {
		// 如果不是ReadSeeker，需要读取所有内容到内存
		data, err := io.ReadAll(file)
		if err != nil {
			return fmt.Errorf("读取文件内容失败：%w", err)
		}
		body = strings.NewReader(string(data))
	}

	// 直接上传文件到R2
	_, err := r.client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(r.config.BucketName),
		Key:         aws.String(fileName),
		Body:        body,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return fmt.Errorf("上传文件失败：%w", err)
	}

	return nil
}
